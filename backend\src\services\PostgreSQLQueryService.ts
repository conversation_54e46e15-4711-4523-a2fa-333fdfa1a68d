import { Pool, PoolClient } from 'pg';

export interface ProtocoloInfo {
  id: number;
  id_protocolo: string;
  data_protocolo: string;
  assunto?: string;
  situacao?: string;
  departamento?: string;
}

export interface EstatisticasGerais {
  protocolos: number;
  requisicoes: number;
  servicos: number;
  departamentos: number;
}

export interface ServicoMunicipal {
  id: number;
  descricao: string;
  id_departamento?: number;
  departamento?: string;
  ativo: boolean;
}

export class PostgreSQLQueryService {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      host: '*************',
      port: 5411,
      user: 'otto',
      password: 'otto',
      database: 'pv_valparaiso',
      max: 10,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    // Configurar eventos do pool
    this.pool.on('error', (err) => {
      console.error('Erro no pool PostgreSQL:', err);
    });
  }

  /**
   * Buscar protocolos por termo específico
   */
  async buscarProtocolos(termo?: string, limite = 10): Promise<ProtocoloInfo[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT DISTINCT
          p.id,
          p.id_protocolo,
          p.data_protocolo::text as data_protocolo,
          COALESCE(a.descricao, 'Não informado') as assunto,
          COALESCE(s.descricao, 'Não informado') as situacao,
          COALESCE(d.descricao, 'Não informado') as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE ($1 = '' OR
          LOWER(a.descricao) LIKE LOWER($1) OR
          LOWER(s.descricao) LIKE LOWER($1) OR
          p.id_protocolo::text LIKE $1)
        ORDER BY data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [
        termo ? `%${termo}%` : '',
        limite
      ]);
      
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Obter estatísticas gerais do sistema
   */
  async obterEstatisticas(): Promise<EstatisticasGerais> {
    const client = await this.pool.connect();
    try {
      const queries = await Promise.all([
        client.query('SELECT COUNT(*) as total FROM protocolo_virtual_processos'),
        client.query('SELECT COUNT(*) as total FROM requisicao'),
        client.query('SELECT COUNT(*) as total FROM servicos'),
        client.query('SELECT COUNT(*) as total FROM departamentos')
      ]);

      return {
        protocolos: parseInt(queries[0].rows[0].total),
        requisicoes: parseInt(queries[1].rows[0].total),
        servicos: parseInt(queries[2].rows[0].total),
        departamentos: parseInt(queries[3].rows[0].total)
      };
    } finally {
      client.release();
    }
  }

  /**
   * Buscar protocolo específico por número
   */
  async buscarProtocoloPorNumero(numero: string): Promise<ProtocoloInfo | null> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          COALESCE(a.descricao, 'Não informado') as assunto,
          COALESCE(s.descricao, 'Não informado') as situacao,
          COALESCE(d.descricao, 'Não informado') as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE p.id_protocolo = $1
      `;
      
      const result = await client.query(query, [numero]);
      return result.rows[0] || null;
    } finally {
      client.release();
    }
  }

  /**
   * Listar serviços municipais por departamento
   */
  async listarServicos(departamentoId?: number): Promise<ServicoMunicipal[]> {
    const client = await this.pool.connect();
    try {
      const query = departamentoId 
        ? `SELECT s.*, d.descricao as departamento 
           FROM servicos s 
           LEFT JOIN departamentos d ON s.id_departamento = d.id
           WHERE s.id_departamento = $1
           ORDER BY s.descricao`
        : `SELECT s.*, d.descricao as departamento 
           FROM servicos s 
           LEFT JOIN departamentos d ON s.id_departamento = d.id
           ORDER BY s.descricao`;
      
      const params = departamentoId ? [departamentoId] : [];
      const result = await client.query(query, params);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar protocolos por situação
   */
  async buscarProtocolosPorSituacao(situacao: string, limite = 20): Promise<ProtocoloInfo[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          a.descricao as assunto,
          s.descricao as situacao,
          d.descricao as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE LOWER(s.descricao) LIKE LOWER($1)
        ORDER BY p.data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [`%${situacao}%`, limite]);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Obter estatísticas por departamento
   */
  async obterEstatisticasPorDepartamento() {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          d.descricao as departamento,
          COUNT(p.id) as total_protocolos,
          COUNT(CASE WHEN s.descricao ILIKE '%andamento%' OR s.descricao ILIKE '%análise%' THEN 1 END) as em_andamento,
          COUNT(CASE WHEN s.descricao ILIKE '%concluído%' OR s.descricao ILIKE '%aprovado%' THEN 1 END) as concluidos
        FROM departamentos d
        LEFT JOIN protocolo_virtual_processos p ON p.departamento_atual = d.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE 1=1
        GROUP BY d.id, d.descricao
        HAVING COUNT(p.id) > 0
        ORDER BY total_protocolos DESC
        LIMIT 10
      `;
      
      const result = await client.query(query);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar alvarás especificamente
   */
  async buscarAlvaras(limite = 10): Promise<ProtocoloInfo[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          p.id,
          p.id_protocolo,
          p.data_protocolo::text,
          a.descricao as assunto,
          s.descricao as situacao,
          d.descricao as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE LOWER(a.descricao) LIKE '%alvará%' 
           OR LOWER(a.descricao) LIKE '%licença%'
           OR LOWER(a.descricao) LIKE '%funcionamento%'
        ORDER BY p.data_protocolo DESC
        LIMIT $1
      `;
      
      const result = await client.query(query, [limite]);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Testar conexão com o banco
   */
  async testarConexao(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      return true;
    } catch (error) {
      console.error('Erro ao testar conexão PostgreSQL:', error);
      return false;
    }
  }

  /**
   * Fechar conexões do pool
   */
  async fecharConexoes(): Promise<void> {
    await this.pool.end();
  }

  /**
   * Buscar protocolos sem referência a requerente (corrigido)
   */
  async buscarProtocolosComRequerente(termo?: string, limite = 10): Promise<any[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT DISTINCT
          p.id,
          p.id_protocolo,
          p.data_protocolo::text as data_protocolo,
          COALESCE(a.descricao, 'Não informado') as assunto,
          COALESCE(s.descricao, 'Não informado') as situacao,
          COALESCE(d.descricao, 'Não informado') as departamento
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE ($1 = '' OR
          LOWER(a.descricao) LIKE LOWER($1) OR
          LOWER(s.descricao) LIKE LOWER($1) OR
          p.id_protocolo::text LIKE $1)
        ORDER BY data_protocolo DESC
        LIMIT $2
      `;
      
      const result = await client.query(query, [
        termo ? `%${termo}%` : '',
        limite
      ]);
      
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar protocolos com TODOS os dados do requerente
   * @param tipo - Filtrar por tipo (alvará, licença, etc)
   * @param limite - Número máximo de resultados
   */
  async buscarProtocolosComDadosCompletos(tipo?: string, limite = 50): Promise<any[]> {
    const client = await this.pool.connect();
    try {
      let query = `
        SELECT 
          -- Dados do protocolo
          p.id,
          p.id_protocolo,
          p.data_protocolo,
          p.observacao,
          p.tipo_protocolo,
          
          -- Dados COMPLETOS do interessado/requerente
          i.id as interessado_id,
          i.nome_razao_social,
          i.cpf_cnpj,
          i.tipo_pessoa,
          i.logradouro as endereco,
          i.bairro,
          i.cidade,
          i.estado,
          i.cep,
          i.numero,
          i.quadra,
          i.lote,
          i.complemento,
          i.nr_telefone as telefone,
          i.nr_celular as celular,
          i.email,
          
          -- Informações complementares
          a.descricao as assunto_descricao,
          s.descricao as situacao_descricao,
          d.descricao as departamento_nome
          
        FROM protocolo_virtual_processos p
        LEFT JOIN protocolo_virtual_interessados i ON p.id_interessado = i.id
        LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE p.id_interessado IS NOT NULL
      `;
      
      const params: any[] = [];
      
      if (tipo) {
        query += ` AND LOWER(a.descricao) LIKE LOWER($${params.length + 1})`;
        params.push(`%${tipo}%`);
      }
      
      query += ` ORDER BY p.data_protocolo DESC LIMIT $${params.length + 1}`;
      params.push(limite);
      
      const result = await client.query(query, params);
      return result.rows;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar cidadão por CPF com histórico completo
   */
  async buscarCidadaoPorCPF(cpf: string): Promise<any> {
    const client = await this.pool.connect();
    try {
      // Dados do cidadão/interessado
      const cidadaoQuery = `
        SELECT * FROM protocolo_virtual_interessados 
        WHERE cpf_cnpj = $1
      `;
      const cidadao = await client.query(cidadaoQuery, [cpf]);
      
      if (cidadao.rows.length === 0) {
        // Tentar buscar em pessoa_fisicas
        const pfQuery = `
          SELECT 
            pf.*,
            p.email
          FROM pessoa_fisicas pf
          LEFT JOIN pessoas p ON pf.id_pessoa = p.id
          WHERE pf.cpf = $1
        `;
        const pf = await client.query(pfQuery, [cpf]);
        
        if (pf.rows.length === 0) return null;
        
        // Se encontrou em pessoa_fisicas, buscar histórico usando o nome
        const historicoPfQuery = `
          SELECT 
            p.id_protocolo,
            p.data_protocolo,
            p.observacao,
            a.descricao as assunto,
            s.descricao as situacao
          FROM protocolo_virtual_processos p
          JOIN protocolo_virtual_interessados i ON p.id_interessado = i.id
          JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
          JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
          WHERE i.nome_razao_social = $1
          ORDER BY p.data_protocolo DESC
        `;
        const historicoPf = await client.query(historicoPfQuery, [pf.rows[0].nome]);
        
        return {
          dadosPessoais: {
            nome_razao_social: pf.rows[0].nome,
            cpf_cnpj: pf.rows[0].cpf,
            email: pf.rows[0].email
          },
          historico: historicoPf.rows
        };
      }
      
      // Histórico de protocolos
      const protocolosQuery = `
        SELECT 
          p.id_protocolo,
          p.data_protocolo,
          p.observacao,
          a.descricao as assunto,
          s.descricao as situacao
        FROM protocolo_virtual_processos p
        JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        WHERE p.id_interessado = $1
        ORDER BY p.data_protocolo DESC
      `;
      const protocolos = await client.query(protocolosQuery, [cidadao.rows[0].id]);
      
      return {
        dadosPessoais: cidadao.rows[0],
        historico: protocolos.rows
      };
    } finally {
      client.release();
    }
  }

  /**
   * Buscar alvarás com dados de empresas e sócios
   */
  async buscarAlvarasComSocios(limite = 20): Promise<any[]> {
    const client = await this.pool.connect();
    try {
      const query = `
        SELECT 
          p.id_protocolo,
          p.data_protocolo,
          p.observacao,
          a.descricao as tipo_alvara,
          s.descricao as situacao,
          
          -- Dados da empresa/interessado
          i.nome_razao_social as empresa,
          i.cpf_cnpj as documento,
          i.tipo_pessoa,
          CONCAT(i.logradouro, ', ', i.numero, 
                 CASE WHEN i.complemento IS NOT NULL THEN ', ' || i.complemento ELSE '' END,
                 ', ', i.bairro, ', ', i.cidade, '/', i.estado) as endereco_completo,
          i.nr_telefone as telefone_empresa,
          i.nr_celular as celular_empresa,
          i.email as email_empresa,
          
          -- Informações adicionais do protocolo
          p.observacao as dados_adicionais,
          d.descricao as departamento_responsavel
          
        FROM protocolo_virtual_processos p
        JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
        JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
        JOIN protocolo_virtual_interessados i ON p.id_interessado = i.id
        LEFT JOIN departamentos d ON p.departamento_atual = d.id
        WHERE LOWER(a.descricao) LIKE '%alvará%' 
           OR LOWER(a.descricao) LIKE '%licença%'
           OR LOWER(a.descricao) LIKE '%licenca%'
        ORDER BY p.data_protocolo DESC
        LIMIT $1
      `;
      
      const result = await client.query(query, [limite]);
      
      // Para cada alvará, buscar possíveis sócios se for empresa (CNPJ)
      const alvarasComDados = result.rows.map(alvara => {
        // Se for pessoa jurídica, indicar que pode haver dados de sócios
        if (alvara.tipo_pessoa === 'PJ' || (alvara.documento && alvara.documento.length > 11)) {
          alvara.observacao_socios = 'Empresa com CNPJ registrado. Dados de sócios podem estar disponíveis mediante consulta específica.';
        }
        return alvara;
      });
      
      return alvarasComDados;
    } finally {
      client.release();
    }
  }
}