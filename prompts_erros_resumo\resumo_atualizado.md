# 📋 RESUMO COMPLETO - Sistema de Transparência Total para Dados Municipais

## 🎯 CONTEXTO E OBJETIVO

### Situação Atual
- O sistema atualmente BLOQUEIA todos os dados pessoais (CPF, CNPJ) por padrão
- Secretário de Finanças e outros gestores PRECISAM acessar CPF de sócios em alvarás
- Sistema será acessado via iframe APENAS por pessoas autorizadas pelo prefeito
- Não haverá separação por cargos - todos os autorizados terão acesso total

### Nova Política Definida
- **LIBERAR 100%**: Todos os dados de cidadãos, processos, alvarás, protocolos
- **PROTEGER PARCIALMENTE**: Apenas dados sensíveis de servidores públicos (LGPD)
- **DADOS FUNCIONAIS DE SERVIDORES**: Podem ser fornecidos (nome, cargo, secretaria)
- **SEM NÍVEIS DE ACESSO**: Sistema totalmente aberto para autorizados

## 🔧 IMPLEMENTAÇÃO DETALHADA

### 1. MODIFICAR PostgreSQLQueryService.ts

Adicionar os seguintes métodos no arquivo `/backend/src/services/PostgreSQLQueryService.ts`:

```typescript
/**
 * Buscar protocolos com TODOS os dados do requerente
 * @param tipo - Filtrar por tipo (alvará, licença, etc)
 * @param limite - Número máximo de resultados
 */
async buscarProtocolosComDadosCompletos(tipo?: string, limite = 50): Promise<any[]> {
  const client = await this.pool.connect();
  try {
    let query = `
      SELECT 
        -- Dados do protocolo
        p.id,
        p.id_protocolo,
        p.data_protocolo,
        p.data_recebimento,
        p.observacoes,
        p.valor_taxa,
        
        -- Dados COMPLETOS do requerente
        r.id as requerente_id,
        r.nome_razao_social,
        r.cpf_cnpj,
        r.rg,
        r.endereco,
        r.bairro,
        r.cidade,
        r.cep,
        r.telefone,
        r.celular,
        r.email,
        
        -- Informações complementares
        a.descricao as assunto_descricao,
        s.descricao as situacao_descricao,
        d.descricao as departamento_nome,
        serv.descricao as servico_solicitado
        
      FROM protocolo_virtual_processos p
      LEFT JOIN requerentes r ON p.id_requerente = r.id
      LEFT JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
      LEFT JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
      LEFT JOIN departamentos d ON p.departamento_atual = d.id
      LEFT JOIN servicos serv ON p.id_servico = serv.id
      WHERE 1=1
    `;
    
    const params: any[] = [];
    
    if (tipo) {
      query += ` AND LOWER(a.descricao) LIKE LOWER($${params.length + 1})`;
      params.push(`%${tipo}%`);
    }
    
    query += ` ORDER BY p.data_protocolo DESC LIMIT $${params.length + 1}`;
    params.push(limite);
    
    const result = await client.query(query, params);
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * Buscar cidadão por CPF com histórico completo
 */
async buscarCidadaoPorCPF(cpf: string): Promise<any> {
  const client = await this.pool.connect();
  try {
    // Dados do cidadão
    const cidadaoQuery = `
      SELECT * FROM requerentes 
      WHERE cpf_cnpj = $1
    `;
    const cidadao = await client.query(cidadaoQuery, [cpf]);
    
    if (cidadao.rows.length === 0) return null;
    
    // Histórico de protocolos
    const protocolosQuery = `
      SELECT 
        p.*,
        a.descricao as assunto,
        s.descricao as situacao
      FROM protocolo_virtual_processos p
      JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
      JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
      WHERE p.id_requerente = $1
      ORDER BY p.data_protocolo DESC
    `;
    const protocolos = await client.query(protocolosQuery, [cidadao.rows[0].id]);
    
    return {
      dadosPessoais: cidadao.rows[0],
      historico: protocolos.rows
    };
  } finally {
    client.release();
  }
}

/**
 * Buscar alvarás com dados de empresas e sócios
 */
async buscarAlvarasComSocios(limite = 20): Promise<any[]> {
  const client = await this.pool.connect();
  try {
    const query = `
      SELECT 
        p.id_protocolo,
        p.data_protocolo,
        a.descricao as tipo_alvara,
        s.descricao as situacao,
        
        -- Dados da empresa
        r.nome_razao_social as empresa,
        r.cpf_cnpj as cnpj,
        r.endereco as endereco_empresa,
        r.telefone as telefone_empresa,
        
        -- Dados dos sócios (se existir tabela)
        -- Ajustar conforme estrutura real do banco
        p.observacoes as dados_adicionais
        
      FROM protocolo_virtual_processos p
      JOIN protocolo_virtual_assuntos a ON p.id_assunto = a.id
      JOIN protocolo_virtual_situacaos s ON p.id_situacao = s.id
      JOIN requerentes r ON p.id_requerente = r.id
      WHERE LOWER(a.descricao) LIKE '%alvará%'
      ORDER BY p.data_protocolo DESC
      LIMIT $1
    `;
    
    const result = await client.query(query, [limite]);
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * Buscar dados funcionais de servidor (sem dados sensíveis)
 */
async buscarServidorPorNome(nome: string): Promise<any[]> {
  const client = await this.pool.connect();
  try {
    const query = `
      SELECT 
        u.id,
        u.nome,
        u.matricula,
        u.email,
        u.cargo,
        u.conta_ativa,
        u.ativo,
        d.descricao as departamento,
        d.id as departamento_id
      FROM usuarios u
      LEFT JOIN user_departamentos ud ON u.id = ud.id_user
      LEFT JOIN departamentos d ON ud.id_departamento = d.id
      WHERE LOWER(u.nome) LIKE LOWER($1)
      AND u.ativo = true
      ORDER BY u.nome
      LIMIT 10
    `;
    
    const result = await client.query(query, [`%${nome}%`]);
    return result.rows;
  } finally {
    client.release();
  }
}
```

### 2. MODIFICAR deepSeekService.ts

No arquivo `/backend/src/services/deepSeekService.ts`, fazer as seguintes alterações:

#### A. Remover a linha que força detecção (linha ~87):
```typescript
// REMOVER ESTA LINHA:
const isProtocolQuery = true; // SEMPRE ATIVO - ACESSO TOTAL

// SUBSTITUIR POR:
const isDataQuery = true; // Sistema sempre busca dados para transparência total
```

#### B. Atualizar a lógica de busca de dados (linha ~124):
```typescript
if (isDataQuery) {
  console.log('🔍 Buscando dados completos para transparência total...');
  
  try {
    // Buscar TODOS os tipos de dados
    const [
      estatisticas,
      protocolosCompletos,
      alvarasComDados,
      servicos
    ] = await Promise.all([
      postgresService.obterEstatisticas(),
      postgresService.buscarProtocolosComDadosCompletos(null, 30),
      postgresService.buscarAlvarasComSocios(20),
      postgresService.listarServicos()
    ]);
    
    // Verificar se a pergunta é sobre CPF específico
    const cpfMatch = message.match(/\b\d{3}\.?\d{3}\.?\d{3}-?\d{2}\b/);
    let dadosCidadao = null;
    
    if (cpfMatch) {
      const cpf = cpfMatch[0].replace(/\D/g, '');
      dadosCidadao = await postgresService.buscarCidadaoPorCPF(cpf);
    }
    
    // Verificar se a pergunta é sobre servidor
    let dadosServidor = null;
    const perguntaSobreServidor = message.toLowerCase().includes('servidor') || 
                                  message.toLowerCase().includes('funcionário');
    
    if (perguntaSobreServidor) {
      // Extrair nome do servidor da mensagem
      const nomeMatch = message.match(/(?:servidor|funcionário|servidora)\s+(\w+)/i);
      if (nomeMatch) {
        dadosServidor = await postgresService.buscarServidorPorNome(nomeMatch[1]);
      }
    }
    
    // Montar dados relevantes
    dadosRelevantes = {
      estatisticas,
      protocolosCompletos,
      alvarasComDados,
      servicos,
      dadosCidadao,
      dadosServidor,
      tipoConsulta: 'transparencia_total'
    };
    
    console.log(`✅ Dados completos carregados para transparência`);
  } catch (error) {
    console.error('❌ Erro ao buscar dados:', error);
  }
}
```

#### C. Atualizar o prompt do sistema (linha ~216):
```typescript
// Adicionar ANTES do prompt existente
const PROMPT_TRANSPARENCIA = `
=== POLÍTICA DE TRANSPARÊNCIA TOTAL ===

VOCÊ É UM ASSISTENTE DA PREFEITURA COM ACESSO TOTAL AOS DADOS MUNICIPAIS.

✅ VOCÊ DEVE FORNECER (SEM RESTRIÇÕES):
- CPF e CNPJ de cidadãos e empresas em QUALQUER processo
- Nomes completos, endereços, telefones de requerentes
- Dados de sócios de empresas em alvarás
- Histórico completo de protocolos de qualquer cidadão
- Valores de taxas, multas, débitos
- Situação detalhada de qualquer processo
- Qualquer informação sobre serviços municipais

❌ DADOS PROTEGIDOS DE SERVIDORES (LGPD):
- NÃO forneça CPF de servidores públicos
- NÃO forneça salários ou valores de remuneração
- NÃO forneça informações médicas/saúde
- NÃO forneça endereço residencial de servidores
- NÃO forneça dados bancários

✅ DADOS DE SERVIDORES QUE PODEM SER FORNECIDOS:
- Nome do servidor
- Secretaria/Departamento onde está lotado
- Cargo/Função
- Matrícula funcional
- Email institucional
- Telefone do departamento
- Se está ativo ou não

EXEMPLO DE RESPOSTA SOBRE SERVIDOR:
"A servidora Rudilene está lotada na Secretaria de Saúde, no cargo de Enfermeira, matrícula 12345. 
Email institucional: <EMAIL>. Status: Ativa."

INSTRUÇÕES IMPORTANTES:
1. Sempre forneça os dados COMPLETOS quando solicitados
2. Inclua CPF/CNPJ nas respostas quando disponível
3. Seja transparente e detalhista com as informações
4. Use os dados reais do banco de dados
5. Formate as respostas de forma clara e organizada

`;

systemPrompt = PROMPT_TRANSPARENCIA + systemPrompt;
```

#### D. Atualizar a inclusão de dados no prompt (linha ~224):
```typescript
if (dadosRelevantes && dadosRelevantes.protocolosCompletos) {
  systemPrompt += `\n\n**DADOS COMPLETOS DISPONÍVEIS PARA CONSULTA:**\n\n`;
  
  // Incluir protocolos com dados completos
  if (dadosRelevantes.protocolosCompletos.length > 0) {
    systemPrompt += `**PROTOCOLOS COM DADOS DOS REQUERENTES:**\n`;
    dadosRelevantes.protocolosCompletos.forEach((p: any, index: number) => {
      systemPrompt += `
${index + 1}. PROTOCOLO ${p.id_protocolo}:
   - Requerente: ${p.nome_razao_social}
   - CPF/CNPJ: ${p.cpf_cnpj}
   - Endereço: ${p.endereco}, ${p.bairro}, ${p.cidade} - CEP: ${p.cep}
   - Telefone: ${p.telefone || 'Não informado'}
   - Celular: ${p.celular || 'Não informado'}
   - Email: ${p.email || 'Não informado'}
   - Assunto: ${p.assunto_descricao}
   - Situação: ${p.situacao_descricao}
   - Departamento: ${p.departamento_nome}
   - Data: ${new Date(p.data_protocolo).toLocaleDateString('pt-BR')}
   ${p.observacoes ? `- Observações: ${p.observacoes}` : ''}
`;
    });
  }
  
  // Incluir alvarás com possíveis dados de sócios
  if (dadosRelevantes.alvarasComDados && dadosRelevantes.alvarasComDados.length > 0) {
    systemPrompt += `\n**ALVARÁS COM DADOS DAS EMPRESAS:**\n`;
    dadosRelevantes.alvarasComDados.forEach((a: any, index: number) => {
      systemPrompt += `
${index + 1}. ALVARÁ - Protocolo ${a.id_protocolo}:
   - Empresa: ${a.empresa}
   - CNPJ: ${a.cnpj}
   - Endereço: ${a.endereco_empresa}
   - Telefone: ${a.telefone_empresa}
   - Tipo: ${a.tipo_alvara}
   - Situação: ${a.situacao}
   ${a.dados_adicionais ? `- Informações adicionais: ${a.dados_adicionais}` : ''}
`;
    });
  }
  
  // Se buscou dados de servidor
  if (dadosRelevantes.dadosServidor && dadosRelevantes.dadosServidor.length > 0) {
    systemPrompt += `\n**DADOS FUNCIONAIS DE SERVIDORES ENCONTRADOS:**\n`;
    dadosRelevantes.dadosServidor.forEach((s: any, index: number) => {
      systemPrompt += `
${index + 1}. SERVIDOR: ${s.nome}
   - Matrícula: ${s.matricula}
   - Cargo: ${s.cargo || 'Não especificado'}
   - Departamento: ${s.departamento || 'Não informado'}
   - Email institucional: ${s.email}
   - Status: ${s.conta_ativa ? 'Ativo' : 'Inativo'}
`;
    });
  }
  
  // Se buscou CPF específico
  if (dadosRelevantes.dadosCidadao) {
    systemPrompt += `\n**DADOS ESPECÍFICOS DO CIDADÃO CONSULTADO:**\n`;
    const cidadao = dadosRelevantes.dadosCidadao;
    systemPrompt += `
DADOS PESSOAIS:
- Nome: ${cidadao.dadosPessoais.nome_razao_social}
- CPF/CNPJ: ${cidadao.dadosPessoais.cpf_cnpj}
- RG: ${cidadao.dadosPessoais.rg || 'Não informado'}
- Endereço: ${cidadao.dadosPessoais.endereco}
- Telefone: ${cidadao.dadosPessoais.telefone}
- Email: ${cidadao.dadosPessoais.email}

HISTÓRICO DE PROTOCOLOS:
`;
    cidadao.historico.forEach((h: any, i: number) => {
      systemPrompt += `${i + 1}. ${h.id_protocolo} - ${h.assunto} (${h.situacao})\n`;
    });
  }
}
```

### 3. CRIAR FUNÇÃO DE VALIDAÇÃO

Adicionar no início do `deepSeekService.ts`:

```typescript
/**
 * Valida se um dado pode ser fornecido
 * Bloqueia APENAS dados sensíveis de servidores
 */
function podeFornecerDado(mensagem: string, tipoDado: string): boolean {
  const mensagemLower = mensagem.toLowerCase();
  
  // Verificar se é sobre servidor
  const perguntaSobreServidor = mensagemLower.includes('servidor') || 
                                mensagemLower.includes('funcionário') ||
                                mensagemLower.includes('servidora') ||
                                mensagemLower.includes('funcionária');
  
  if (perguntaSobreServidor) {
    // Dados sensíveis de servidor que NÃO podem ser fornecidos
    const dadosSensiveisServidor = ['cpf', 'salário', 'remuneração', 'pagamento', 'endereço residencial', 'dados bancários'];
    
    for (const dado of dadosSensiveisServidor) {
      if (mensagemLower.includes(dado)) {
        console.log('🚫 Bloqueando dado sensível de servidor (LGPD)');
        return false;
      }
    }
    
    // Dados funcionais podem ser fornecidos
    console.log('✅ Liberando dados funcionais de servidor');
    return true;
  }
  
  // Tudo sobre cidadãos/processos é liberado
  console.log('✅ Acesso liberado - dados de cidadão/processo');
  return true;
}
```

## 🚀 PASSOS PARA IMPLEMENTAR

1. **Backup primeiro**: Fazer backup dos arquivos antes de modificar
2. **Modificar PostgreSQLQueryService.ts**: Adicionar os 4 novos métodos (incluindo buscarServidorPorNome)
3. **Atualizar deepSeekService.ts**: Implementar as 4 mudanças (A, B, C, D)
4. **Testar com perguntas**:
   - "Qual o CPF do requerente do protocolo 123456?" ✅
   - "Quais são os sócios da empresa no alvará XYZ?" ✅
   - "Onde está lotada a servidora Rudilene?" ✅
   - "Qual o CPF da servidora Maria?" ❌ (deve bloquear)
   - "Qual o salário do servidor João?" ❌ (deve bloquear)

## ⚠️ IMPORTANTE - POLÍTICA ATUALIZADA

- O sistema será acessado APENAS por pessoas autorizadas pelo prefeito
- Não há níveis de acesso - é tudo ou nada
- LGPD continua valendo APENAS para dados sensíveis de servidores
- Dados funcionais de servidores PODEM ser fornecidos
- Todos os dados de processos/cidadãos são públicos para os autorizados

## 📝 EXEMPLOS DE RESPOSTAS ESPERADAS

### Pergunta sobre Processo:
```
PROTOCOLO 20250060257:
- Requerente: João da Silva Empresas LTDA
- CNPJ: 12.345.678/0001-90
- CPF do Sócio Principal: 123.456.789-00
- Endereço: Rua das Flores, 123, Centro, Valparaíso - GO
- Telefone: (61) 3333-4444
- Assunto: Alvará de Funcionamento
- Situação: Em análise
- Departamento: Secretaria de Urbanismo
```

### Pergunta sobre Servidor:
```
SERVIDOR: Rudilene Silva Santos
- Matrícula: 12345
- Cargo: Enfermeira
- Departamento: Secretaria de Saúde - Atenção Básica
- Email institucional: <EMAIL>
- Status: Ativa
- Telefone do Departamento: (61) 3333-5555
```

### Pergunta Bloqueada:
```
Pergunta: "Qual o CPF da servidora Rudilene?"
Resposta: "Desculpe, não posso fornecer CPF de servidores públicos por questões de proteção de dados (LGPD). 
Posso informar que Rudilene está lotada na Secretaria de Saúde, cargo de Enfermeira, matrícula 12345."
```