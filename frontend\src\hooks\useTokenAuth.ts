/**
 * Hook para gerenciar autenticação automática por token de acesso
 */

import { useState, useEffect, useCallback } from 'react';

interface TokenAuthData {
  token: string | null;
  isAuthenticated: boolean;
  user: any | null;
  isLoading: boolean;
  error: string | null;
}

interface UseTokenAuthReturn extends TokenAuthData {
  setToken: (token: string) => void;
  logout: () => void;
  refreshAuth: () => Promise<void>;
}

const TOKEN_STORAGE_KEY = 'chatbot_access_token';
const USER_STORAGE_KEY = 'chatbot_user_data';

export function useTokenAuth(): UseTokenAuthReturn {
  const [authData, setAuthData] = useState<TokenAuthData>({
    token: null,
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null
  });

  /**
   * Capturar token da URL ou localStorage
   */
  const initializeToken = useCallback(() => {
    try {
      // 1. Verificar se há token na URL
      const urlParams = new URLSearchParams(window.location.search);
      const urlToken = urlParams.get('token');

      if (urlToken) {
        // Token encontrado na URL - salvar e limpar URL
        localStorage.setItem(TOKEN_STORAGE_KEY, urlToken);
        
        // Limpar token da URL sem recarregar a página
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('token');
        window.history.replaceState({}, '', newUrl.toString());
        
        return urlToken;
      }

      // 2. Verificar localStorage se não há token na URL
      const storedToken = localStorage.getItem(TOKEN_STORAGE_KEY);
      return storedToken;

    } catch (error) {
      console.error('Erro ao inicializar token:', error);
      return null;
    }
  }, []);

  /**
   * Validar token no backend
   */
  const validateToken = useCallback(async (token: string) => {
    try {
      const response = await fetch('/api/auth/validate-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Access-Token': token
        },
        body: JSON.stringify({ token })
      });

      if (!response.ok) {
        throw new Error('Token inválido');
      }

      const data = await response.json();
      
      if (data.success && data.user) {
        // Salvar dados do usuário
        localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(data.user));
        
        return {
          isValid: true,
          user: data.user
        };
      }

      return { isValid: false, user: null };

    } catch (error) {
      console.error('Erro ao validar token:', error);
      return { isValid: false, user: null };
    }
  }, []);

  /**
   * Configurar token e autenticar
   */
  const setToken = useCallback(async (token: string) => {
    setAuthData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      localStorage.setItem(TOKEN_STORAGE_KEY, token);
      
      const validation = await validateToken(token);
      
      if (validation.isValid) {
        setAuthData({
          token,
          isAuthenticated: true,
          user: validation.user,
          isLoading: false,
          error: null
        });
      } else {
        // Token inválido - limpar dados
        localStorage.removeItem(TOKEN_STORAGE_KEY);
        localStorage.removeItem(USER_STORAGE_KEY);
        
        setAuthData({
          token: null,
          isAuthenticated: false,
          user: null,
          isLoading: false,
          error: 'Token de acesso inválido'
        });
      }
    } catch (error) {
      setAuthData({
        token: null,
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: 'Erro ao autenticar'
      });
    }
  }, [validateToken]);

  /**
   * Fazer logout
   */
  const logout = useCallback(() => {
    localStorage.removeItem(TOKEN_STORAGE_KEY);
    localStorage.removeItem(USER_STORAGE_KEY);
    
    setAuthData({
      token: null,
      isAuthenticated: false,
      user: null,
      isLoading: false,
      error: null
    });
  }, []);

  /**
   * Recarregar dados de autenticação
   */
  const refreshAuth = useCallback(async () => {
    const token = localStorage.getItem(TOKEN_STORAGE_KEY);
    
    if (token) {
      await setToken(token);
    } else {
      setAuthData({
        token: null,
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null
      });
    }
  }, [setToken]);

  /**
   * Inicializar autenticação na montagem do componente
   */
  useEffect(() => {
    const initAuth = async () => {
      const token = initializeToken();
      
      if (token) {
        await setToken(token);
      } else {
        // Verificar se há dados do usuário salvos
        const savedUser = localStorage.getItem(USER_STORAGE_KEY);
        
        if (savedUser) {
          try {
            const user = JSON.parse(savedUser);
            setAuthData({
              token: null,
              isAuthenticated: false,
              user,
              isLoading: false,
              error: 'Token não encontrado - acesso limitado'
            });
          } catch (error) {
            setAuthData({
              token: null,
              isAuthenticated: false,
              user: null,
              isLoading: false,
              error: null
            });
          }
        } else {
          setAuthData({
            token: null,
            isAuthenticated: false,
            user: null,
            isLoading: false,
            error: null
          });
        }
      }
    };

    initAuth();
  }, [initializeToken, setToken]);

  return {
    token: authData.token,
    isAuthenticated: authData.isAuthenticated,
    user: authData.user,
    isLoading: authData.isLoading,
    error: authData.error,
    setToken,
    logout,
    refreshAuth
  };
}