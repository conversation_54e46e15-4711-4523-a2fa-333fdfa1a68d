🚨 PROMPT PARA CORREÇÃO DO ERRO DO SIDEBAR - CHATBOT PREFEITURA

  CONTEXTO DO SISTEMA

  Estou desenvolvendo um chatbot para a Prefeitura de Valparaíso de Goiás com as seguintes tecnologias:
  - Backend: Node.js + TypeScript + Express
  - Frontend: Next.js 14 + React + TypeScript
  - Banco de Dados: PostgreSQL (*************:5411) + MongoDB + Redis
  - Autenticação: JWT com mock de usuários

  PROBLEMA PRINCIPAL

  O sidebar do chat não está carregando o histórico de conversas. O chat funciona perfeitamente, responde corretamente, mas as conversas não aparecem no histórico do sidebar.

  ERRO ESPECÍFICO

  Erro ao garantir conversa: error: invalid input syntax for type bigint: "NaN"
      at PostgreSQLRepository.findUserById (backend/src/repositories/PostgreSQLRepository.ts:261:22)
      at Function.ensureConversation (backend/src/controllers/conversationController.ts:47:20)
      at processMessage (backend/src/controllers/chatController.ts:78:34)

  DETALHES TÉCNICOS

  1. Fluxo de Autenticação:
    - Sistema usa mock de usuários (não busca do PostgreSQL real)
    - Login com: <EMAIL> / admin123
    - JWT gerado com userId: 'user' (string hardcoded)
  2. Estrutura da Tabela conversations:
  - id (uuid)
  - user_id (integer)
  - title (character varying)
  - status (character varying)
  - created_at (timestamp)
  - updated_at (timestamp)
  - message_count (integer)
  - last_message (text)
  3. Código com Problema:
  // PostgreSQLRepository.ts
  async findUserById(userId: string): Promise<Usuario | null> {
    const query = 'SELECT * FROM usuarios WHERE id = $1';
    const result = await client.query(query, [parseInt(userId)]); // ERRO: parseInt('user') = NaN
    return result.rows[0] || null;
  }
  4. Fluxo do Erro:
    - Chat envia mensagem → chatController.ts
    - Tenta criar/buscar conversa → conversationController.ensureConversation(userId)
    - Chama postgresRepository.findUserById('user')
    - parseInt('user') retorna NaN
    - PostgreSQL rejeita: "invalid input syntax for type bigint: NaN"

  O QUE PRECISA SER CORRIGIDO

  1. Sistema de Mock vs Real:
    - O sistema usa autenticação mock mas tenta buscar usuário real no PostgreSQL
    - Precisa consistência: ou usa mock completo ou dados reais
  2. Conversas não aparecem no sidebar:
    - Mesmo que o chat funcione, as conversas não são salvas
    - O sidebar sempre mostra "Nenhuma conversa ainda"
  3. Tipo de userId:
    - Mock usa userId: 'user' (string)
    - Banco espera user_id como integer
    - Conversão falha causando o erro

  ARQUIVOS RELEVANTES

  - /backend/src/repositories/PostgreSQLRepository.ts - método findUserById
  - /backend/src/controllers/conversationController.ts - ensureConversation
  - /backend/src/controllers/chatController.ts - processMessage
  - /backend/src/middleware/authenticateMiddleware.ts - onde define userId como 'user'
  - /frontend/src/components/ChatInterface.tsx - componente do chat com sidebar

  SOLUÇÃO ESPERADA

  Preciso que o sistema:
  1. Salve as conversas corretamente no banco
  2. Mostre o histórico de conversas no sidebar
  3. Mantenha consistência entre mock e banco real
  4. Corrija o erro de tipo "NaN" no userId

  Por favor, analise o problema e sugira as correções necessárias para fazer o sidebar funcionar corretamente, mantendo a compatibilidade com o sistema mock de autenticação.