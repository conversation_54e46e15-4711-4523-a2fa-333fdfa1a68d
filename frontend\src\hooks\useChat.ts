import { useState, useCallback } from 'react';
import { ChatMessage, ApiResponse } from '@/types';

interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  conversationId: string | null;
}

interface SendMessageParams {
  message: string;
  conversationId?: string;
}

interface SendMessageResponse {
  success: boolean;
  response?: string;
  data?: {
    response: string;
    source: string;
    cost?: number;
    timestamp: string;
  };
  metadata?: {
    processingTime: number;
    urgency: string;
    discountActive: boolean;
    tokens?: {
      input: number;
      output: number;
      total: number;
    };
  };
  error?: string;
}

export function useChat(accessToken?: string, user?: any) {
  const [state, setState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    error: null,
    conversationId: null
  });

  const sendMessage = useCallback(async ({ message, conversationId }: SendMessageParams) => {
    if (!message.trim()) return;

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    // Adicionar mensagem do usuário imediatamente
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      conversationId: conversationId || 'temp',
      userId: 'user',
      content: message,
      type: 'user',
      timestamp: new Date()
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage]
    }));

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Adicionar token de acesso se disponível
      if (accessToken) {
        headers['X-Access-Token'] = accessToken;
      }

      const response = await fetch('http://localhost:3001/api/chat/message', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          message,
          userId: user?.id || 'user',
          secretaria: 'administracao',
          conversationId: conversationId || undefined
        })
      });

      const data: SendMessageResponse = await response.json();

      if (data.success && data.data?.response) {
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          conversationId: conversationId || 'temp',
          userId: 'assistant',
          content: data.data.response,
          type: 'assistant',
          timestamp: new Date(),
          metadata: {
            responseTime: data.metadata?.processingTime,
            tokensUsed: data.metadata?.tokens?.total,
            cost: data.data.cost
          }
        };

        setState(prev => ({
          ...prev,
          messages: [...prev.messages, assistantMessage],
          isLoading: false,
          conversationId: conversationId || prev.conversationId || 'temp'
        }));

        return data;
      } else {
        throw new Error(data.error || 'Erro ao enviar mensagem');
      }
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      }));
      throw error;
    }
  }, []);

  const clearMessages = useCallback(() => {
    setState({
      messages: [],
      isLoading: false,
      error: null,
      conversationId: null
    });
  }, []);

  const setConversationId = useCallback((id: string) => {
    setState(prev => ({
      ...prev,
      conversationId: id
    }));
  }, []);

  return {
    messages: state.messages,
    isLoading: state.isLoading,
    error: state.error,
    conversationId: state.conversationId,
    sendMessage,
    clearMessages,
    setConversationId
  };
}