'use client';

import { useState, useEffect } from 'react';
import { Logo } from '@/components/ui';
import { ChatWindow, ConversationList, MessageInput } from '@/components/chat';
import { useChat } from '@/hooks/useChat';
import { useConversations } from '@/hooks/useConversations';
import { useTokenAuth } from '@/hooks/useTokenAuth';

export default function ChatPage() {
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  
  // Autenticação por token
  const { token, isAuthenticated, user, isLoading: authLoading, error: authError } = useTokenAuth();
  
  const { messages, isLoading, sendMessage, clearMessages } = useChat(token, user);
  const { 
    conversations, 
    loading: conversationsLoading,
    createConversation,
    deleteConversation,
    getOrCreateActiveConversation
  } = useConversations();

  // Criar conversa ativa ao carregar
  useEffect(() => {
    if (!authLoading && !authError && isAuthenticated) {
      const initConversation = async () => {
        try {
          const conversation = await getOrCreateActiveConversation();
          setActiveConversationId(conversation.id);
        } catch (error) {
          console.error('Erro ao inicializar conversa:', error);
        }
      };
      
      initConversation();
    }
  }, [authLoading, authError, isAuthenticated, getOrCreateActiveConversation]);

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage({ message, conversationId: activeConversationId || undefined });
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  const handleNewConversation = async () => {
    try {
      const newConversation = await createConversation('Nova Conversa');
      setActiveConversationId(newConversation.id);
      clearMessages();
    } catch (error) {
      console.error('Erro ao criar conversa:', error);
    }
  };

  const handleSelectConversation = (conversationId: string) => {
    // TODO: Carregar mensagens da conversa selecionada
    setActiveConversationId(conversationId);
    console.log('Carregar conversa:', conversationId);
  };

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId);
      
      // Se deletar a conversa ativa, criar uma nova
      if (conversationId === activeConversationId) {
        handleNewConversation();
      }
    } catch (error) {
      console.error('Erro ao deletar conversa:', error);
    }
  };

  // Mostrar loading enquanto autentica
  if (authLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-pv-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pv-blue-primary mx-auto mb-4"></div>
          <p className="text-pv-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  // Mostrar erro se não conseguir autenticar
  if (authError) {
    return (
      <div className="h-screen flex items-center justify-center bg-pv-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-pv-gray-800 mb-2">Acesso Negado</h1>
          <p className="text-pv-gray-600 mb-4">{authError}</p>
          <p className="text-sm text-pv-gray-500">
            Você precisa de um link autorizado pelo Prefeito para acessar este chatbot.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-pv-blue-primary to-pv-blue-secondary text-white shadow-lg">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Logo variant="horizontal" size="sm" />
              <div>
                <h1 className="text-xl font-semibold">Chatbot Inteligente</h1>
                <p className="text-pv-blue-100 text-sm">Prefeitura de Valparaíso de Goiás</p>
                {user && user.isAnonymous && (
                  <p className="text-xs text-pv-blue-200">
                    👤 Usuário Autorizado • {user.description || 'Acesso Liberado'}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Status Indicator */}
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-pv-blue-100">Sistema Online</span>
              </div>
              
              {/* Help Button */}
              <button className="p-2 hover:bg-pv-blue-600 rounded-lg transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar - Conversation List - Hidden on mobile by default */}
        <div className="hidden lg:block">
          <ConversationList
          conversations={conversations}
          activeConversationId={activeConversationId}
          onSelectConversation={handleSelectConversation}
          onNewConversation={handleNewConversation}
          onDeleteConversation={handleDeleteConversation}
          />
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Header */}
          <div className="border-b border-pv-gray-200 bg-white px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-pv-gray-800">
                  {activeConversationId 
                    ? conversations.find(c => c.id === activeConversationId)?.title || 'Conversa'
                    : 'Nova Conversa'
                  }
                </h2>
                <p className="text-sm text-pv-gray-600">
                  Consulte informações municipais, departamentos e serviços
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* Export Button */}
                <button 
                  onClick={() => {
                    if (activeConversationId) {
                      window.open(`/api/conversations/${activeConversationId}/export`, '_blank');
                    }
                  }}
                  className="p-2 text-pv-gray-500 hover:text-pv-blue-primary hover:bg-pv-blue-50 rounded-lg transition-colors"
                  title="Exportar conversa"
                  disabled={!activeConversationId}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </button>
                
                {/* Clear Chat Button */}
                <button 
                  onClick={() => {
                    if (window.confirm('Deseja realmente limpar esta conversa e iniciar uma nova?')) {
                      handleNewConversation();
                    }
                  }}
                  className="p-2 text-pv-gray-500 hover:text-pv-blue-primary hover:bg-pv-blue-50 rounded-lg transition-colors"
                  title="Nova conversa"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Chat Messages */}
          <ChatWindow 
            messages={messages} 
            isLoading={isLoading} 
            onQuickAction={handleSendMessage}
          />

          {/* Message Input */}
          <MessageInput 
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            placeholder="Faça uma pergunta sobre serviços municipais, departamentos ou funcionários..."
          />
        </div>
      </div>

      {/* Footer */}
      <footer className="border-t border-pv-gray-200 bg-pv-gray-50 px-6 py-3">
        <div className="flex items-center justify-between text-sm text-pv-gray-600">
          <div className="flex items-center space-x-4">
            <span>© 2025 Prefeitura de Valparaíso de Goiás</span>
            <span>•</span>
            <span>Sistema de IA Municipal</span>
          </div>
          
      
        </div>
      </footer>
    </div>
  );
}