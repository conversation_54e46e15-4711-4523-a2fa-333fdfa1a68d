import { Request, Response } from 'express';
import { CachedRequest } from '../middleware/cacheMiddleware';
import { postgresRepository } from '../repositories/PostgreSQLRepository';

class ConversationController {
  // Salvar mensagem na conversa (usado internamente pelo ChatController)
  static async saveMessage(
    conversationId: string,
    userId: string,
    content: string,
    role: 'user' | 'assistant',
    metadata?: {
      responseTime?: number;
      tokensUsed?: number;
      cost?: number;
    }
  ): Promise<void> {
    try {
      // Verificar se o usuário tem acesso à conversa
      const user = await postgresRepository.findUserById(userId);
      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Salvar mensagem no PostgreSQL
      await postgresRepository.addMessage({
        conversationId,
        content,
        role,
        tokensUsed: metadata?.tokensUsed,
        cost: metadata?.cost,
        cacheHit: false,
        processingTime: metadata?.responseTime,
        metadata: metadata || {}
      });

      console.log(`💾 Mensagem salva na conversa ${conversationId}`);
    } catch (error) {
      console.error('Erro ao salvar mensagem:', error);
      throw error;
    }
  }

  // Criar conversa se não existir
  static async ensureConversation(userId: string): Promise<string> {
    try {
      const user = await postgresRepository.findUserById(userId);
      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // Buscar ou criar conversa ativa
      const conversation = await postgresRepository.getOrCreateActiveConversation(user.id);
      return conversation.id;
    } catch (error) {
      console.error('Erro ao garantir conversa:', error);
      throw error;
    }
  }
}

export default ConversationController;